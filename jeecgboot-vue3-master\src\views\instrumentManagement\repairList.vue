<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    :title="'仪器维修记录'"
    @ok="handleSubmit"
    :width="1400"
    :height="600"
  >
    <div class="repair-list-container">
      <!-- 搜索表单 -->
      <div class="search-form">
        <BasicForm @register="registerForm" @submit="handleSearch" @reset="handleReset" />
      </div>

      <!-- 维修记录表格 -->
      <div class="repair-table">
        <BasicTable @register="registerTable" :scroll="{ y: 400 }">
          <!-- 审批状态列 -->
          <template #spStatus="{ record }">
            <a-tag :color="getStatusColor(record.spStatus)">
              {{ getStatusText(record.spStatus) }}
            </a-tag>
          </template>

          <!-- 紧急程度列 -->
          <template #urgencyLevel="{ record }">
            <a-tag :color="getUrgencyColor(record.urgencyLevel)">
              {{ record.urgencyLevel || '-' }}
            </a-tag>
          </template>

          <!-- 操作列 -->
          <template #action="{ record }">
            <TableAction :actions="getTableAction(record)" />
          </template>

          <!-- 展开行内容 -->
          <template #expandedRowRender="{ record }">
            <div class="expanded-content">
              <a-descriptions title="维修详细信息" :column="2" bordered size="small">
                <a-descriptions-item label="企微审批编号">
                  {{ record.spNo || '-' }}
                </a-descriptions-item>
                <a-descriptions-item label="维修单位">
                  {{ record.unit || '-' }}
                </a-descriptions-item>
                <a-descriptions-item label="地点类型">
                  {{ record.place || '-' }}
                </a-descriptions-item>
                <a-descriptions-item label="创建时间">
                  {{ formatDate(record.createTime) }}
                </a-descriptions-item>
                <a-descriptions-item label="创建人">
                  {{ record.creator || '-' }}
                </a-descriptions-item>
                <a-descriptions-item label="报修原因" :span="2">
                  {{ record.reason || '-' }}
                </a-descriptions-item>
              </a-descriptions>

              <!-- 维修详情记录 -->
              <div class="detail-section" v-if="record.details && record.details.length > 0">
                <h4>维修详情记录</h4>
                <a-table
                  :columns="detailColumns"
                  :data-source="record.details"
                  :pagination="false"
                  size="small"
                  row-key="id"
                >
                  <template #fileUrl="{ record: detail }">
                    <a-space v-if="detail.fileUrl">
                      <a-button
                        type="link"
                        size="small"
                        @click="handlePreviewFile(detail.fileUrl)"
                      >
                        查看附件
                      </a-button>
                    </a-space>
                    <span v-else>-</span>
                  </template>
                </a-table>
              </div>
              <div v-else class="no-detail">
                <a-empty description="暂无维修详情记录" />
              </div>
            </div>
          </template>
        </BasicTable>
      </div>
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref, reactive, unref } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, useForm, FormSchema } from '/@/components/Form/index';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { defHttp } from '/@/utils/http/axios';
  import { useUserStore } from '/@/store/modules/user';
  import { message } from 'ant-design-vue';
  import dayjs from 'dayjs';

  const userStore = useUserStore();

  // 声明Emits
  const emit = defineEmits(['success', 'register']);

  // 当前仪器ID
  const currentInstrumentId = ref('');

  // 搜索表单配置
  const searchFormSchema: FormSchema[] = [
    {
      field: 'spStatus',
      label: '审批状态',
      component: 'Select',
      componentProps: {
        placeholder: '请选择审批状态',
        options: [
          { label: '全部', value: '' },
          { label: '审批中', value: '1' },
          { label: '已通过', value: '2' },
          { label: '已驳回', value: '3' },
          { label: '已撤销', value: '4' },
          { label: '通过后撤销', value: '6' },
          { label: '已删除', value: '7' },
          { label: '已支付', value: '10' }
        ]
      },
      colProps: { span: 8 }
    },
    {
      field: 'urgencyLevel',
      label: '紧急程度',
      component: 'Select',
      componentProps: {
        placeholder: '请选择紧急程度',
        options: [
          { label: '全部', value: '' },
          { label: '紧急', value: '紧急' },
          { label: '一般', value: '一般' },
          { label: '不紧急', value: '不紧急' }
        ]
      },
      colProps: { span: 8 }
    },
    {
      field: 'type',
      label: '报修类型',
      component: 'Input',
      componentProps: {
        placeholder: '请输入报修类型'
      },
      colProps: { span: 8 }
    }
  ];

  // 注册搜索表单
  const [registerForm, { getFieldsValue, resetFields }] = useForm({
    labelWidth: 80,
    schemas: searchFormSchema,
    autoSubmitOnEnter: true,
    submitButtonOptions: {
      text: '查询'
    },
    resetButtonOptions: {
      text: '重置'
    },
    actionColOptions: {
      span: 24
    },
    baseColProps: {
      span: 8
    }
  });

  // 主表列配置
  const columns = [
    {
      title: '企微审批编号',
      dataIndex: 'spNo',
      width: 150,
      ellipsis: true
    },
    {
      title: '审批状态',
      dataIndex: 'spStatus',
      width: 100,
      slots: { customRender: 'spStatus' }
    },
    {
      title: '紧急程度',
      dataIndex: 'urgencyLevel',
      width: 100,
      slots: { customRender: 'urgencyLevel' }
    },
    {
      title: '所属部门',
      dataIndex: 'department',
      width: 150,
      ellipsis: true
    },
    {
      title: '报修类型',
      dataIndex: 'type',
      width: 120
    },
    {
      title: '维修单位',
      dataIndex: 'unit',
      width: 150,
      ellipsis: true
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 150,
      customRender: ({ text }) => formatDate(text)
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      fixed: 'right',
      slots: { customRender: 'action' }
    }
  ];

  // 详情表列配置
  const detailColumns = [
    {
      title: '联系电话',
      dataIndex: 'phoneNumber',
      width: 120
    },
    {
      title: '地点',
      dataIndex: 'place',
      width: 150
    },
    {
      title: '故障描述',
      dataIndex: 'faultDescription',
      ellipsis: true
    },
    {
      title: '附件',
      dataIndex: 'fileUrl',
      width: 100,
      slots: { customRender: 'fileUrl' }
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 150,
      customRender: ({ text }) => formatDate(text)
    }
  ];

  // API接口
  const getMaintenanceList = (params: any) => {
    return defHttp.get({
      url: '/instrument/maintenance/list',
      params: {
        ...params,
        instrumentId: currentInstrumentId.value
      }
    });
  };

  const getMaintenanceDetailList = (mainId: string) => {
    return defHttp.get({
      url: '/instrument/maintenance/detail/list',
      params: { mainId }
    });
  };

  // 注册表格
  const [registerTable, { reload }] = useTable({
    api: getMaintenanceList,
    columns,
    useSearchForm: false,
    showTableSetting: false,
    bordered: true,
    showIndexColumn: false,
    canResize: false,
    expandRowByClick: true,
    pagination: {
      pageSize: 10,
      showSizeChanger: true,
      showQuickJumper: true
    },
    beforeFetch: (params: any) => {
      const searchParams = getFieldsValue();
      return {
        ...params,
        ...searchParams,
        instrumentId: currentInstrumentId.value
      };
    }
  });

  // 模态框注册
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data: any) => {
    setModalProps({ confirmLoading: false });

    if (data?.instrumentId) {
      currentInstrumentId.value = data.instrumentId;
      // 重新加载数据
      await reload();
    }
  });

  // 获取审批状态颜色
  const getStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
      '1': 'processing',
      '2': 'success',
      '3': 'error',
      '4': 'default',
      '6': 'warning',
      '7': 'default',
      '10': 'cyan'
    };
    return colorMap[status] || 'default';
  };

  // 获取审批状态文本
  const getStatusText = (status: string) => {
    const textMap: Record<string, string> = {
      '1': '审批中',
      '2': '已通过',
      '3': '已驳回',
      '4': '已撤销',
      '6': '通过后撤销',
      '7': '已删除',
      '10': '已支付'
    };
    return textMap[status] || '未知';
  };

  // 获取紧急程度颜色
  const getUrgencyColor = (level: string) => {
    const colorMap: Record<string, string> = {
      '紧急': 'red',
      '一般': 'orange',
      '不紧急': 'green'
    };
    return colorMap[level] || 'default';
  };

  // 格式化日期
  const formatDate = (date: string) => {
    return date ? dayjs(date).format('YYYY-MM-DD HH:mm:ss') : '-';
  };

  // 搜索
  const handleSearch = () => {
    reload();
  };

  // 重置
  const handleReset = () => {
    resetFields();
    reload();
  };

  // 查看详情
  const handleView = (record: any) => {
    message.info('查看详情功能');
  };

  // 预览文件
  const handlePreviewFile = (fileUrl: string) => {
    window.open(fileUrl, '_blank');
  };

  // 表格操作
  const getTableAction = (record: any) => {
    return [
      {
        label: '查看',
        onClick: handleView.bind(null, record)
      }
    ];
  };

  // 表单提交事件
  async function handleSubmit() {
    closeModal();
  }
</script>

<style scoped>
.repair-list-container {
  padding: 16px;
}

.search-form {
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.repair-table {
  background: #fff;
}

.expanded-content {
  padding: 16px;
  background: #f9f9f9;
}

.detail-section {
  margin-top: 16px;
}

.detail-section h4 {
  margin-bottom: 12px;
  color: #1890ff;
}

.no-detail {
  text-align: center;
  padding: 20px;
}
</style>
