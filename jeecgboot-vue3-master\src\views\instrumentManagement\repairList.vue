<template>

  <div class="repair-list-container">
    <!-- 搜索表单 -->
    <div class="search-form">
      <BasicForm @register="registerForm" @submit="handleSearch" @reset="handleReset" />
    </div>

    <!-- 工具栏 -->
    <div class="toolbar">
      <a-space>
        <a-button type="primary" @click="handleExport" :loading="exportLoading">
          <template #icon>
            <ExportOutlined />
          </template>
          导出Excel
        </a-button>
        <a-button @click="handlePrint">
          <template #icon>
            <ReloadOutlined />
          </template>
          打印
        </a-button>
      </a-space>

      <!-- 分页信息显示 -->
      <div class="pagination-info">
        <span>共 {{ paginationInfo.total }} 条记录</span>
      </div>
    </div>

    <!-- 维修记录表格 -->
    <div class="repair-table">
      <BasicTable @register="registerTable" :scroll="{ y: 350 }">
        <!-- 审批状态列 -->
        <template #spStatus="{ record }">
          <a-tag :color="getStatusColor(record.spStatus)">
            {{ getStatusText(record.spStatus) }}
          </a-tag>
        </template>

        <!-- 紧急程度列 -->
        <template #urgencyLevel="{ record }">
          <a-tag :color="getUrgencyColor(record.urgencyLevel)">
            {{ record.urgencyLevel || '-' }}
          </a-tag>
        </template>

        <!-- 操作列 -->
        <template #action="{ record }">
          <TableAction :actions="getTableAction(record)" />
        </template>

        <!-- 展开行内容 -->
        <template #expandedRowRender="{ record }">
          <div class="expanded-content">
            <a-descriptions title="维修详细信息" :column="2" bordered size="small">
              <a-descriptions-item label="企微审批编号">
                {{ record.spNo || '-' }}
              </a-descriptions-item>
              <a-descriptions-item label="维修单位">
                {{ record.unit || '-' }}
              </a-descriptions-item>
              <a-descriptions-item label="地点类型">
                {{ record.place || '-' }}
              </a-descriptions-item>
              <a-descriptions-item label="创建时间">
                {{ formatDate(record.createTime) }}
              </a-descriptions-item>
              <a-descriptions-item label="创建人">
                {{ record.creator || '-' }}
              </a-descriptions-item>
              <a-descriptions-item label="报修原因" :span="2">
                {{ record.reason || '-' }}
              </a-descriptions-item>
            </a-descriptions>

            <!-- 维修详情记录 -->
            <div class="detail-section" v-if="record.details && record.details.length > 0">
              <h4>维修详情记录</h4>
              <a-table :columns="detailColumns" :data-source="record.details" :pagination="false" size="small"
                row-key="id">
                <template #fileUrl="{ record: detail }">
                  <a-space v-if="detail.fileUrl">
                    <a-button type="link" size="small" @click="handlePreviewFile(detail.fileUrl)">
                      查看附件
                    </a-button>
                  </a-space>
                  <span v-else>-</span>
                </template>
              </a-table>
            </div>
            <div v-else class="no-detail">
              <a-empty description="暂无维修详情记录" />
            </div>
          </div>
        </template>
      </BasicTable>
    </div>
  </div>
</template>

<script lang="ts" setup name='instrumentManagement-repairList'>
import { ref, reactive } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { BasicForm, useForm, FormSchema } from '/@/components/Form/index';
import { BasicTable, useTable, TableAction } from '/@/components/Table';
import { defHttp } from '/@/utils/http/axios';
import { useUserStore } from '/@/store/modules/user';
import { message } from 'ant-design-vue';
import { ExportOutlined, ReloadOutlined } from '@ant-design/icons-vue';
import dayjs from 'dayjs';
import { hiprint } from 'vue-plugin-hiprint';
import panel from './repairListPrintjs.js'; //模板
const userStore = useUserStore();
// 声明Emits
const emit = defineEmits(['success', 'register']);

// 当前仪器ID
const currentInstrumentId = ref('');

// 导出加载状态
const exportLoading = ref(false);

// 分页信息
const paginationInfo = reactive({
  current: 1,
  pageSize: 10,
  total: 0
});

// 搜索表单配置
const searchFormSchema: FormSchema[] = [
  {
    field: 'spStatus',
    label: '审批状态',
    component: 'Select',
    componentProps: {
      placeholder: '请选择审批状态',
      options: [
        { label: '全部', value: '' },
        { label: '审批中', value: '1' },
        { label: '已通过', value: '2' },
        { label: '已驳回', value: '3' },
        { label: '已撤销', value: '4' },
        { label: '通过后撤销', value: '6' },
        { label: '已删除', value: '7' },
        { label: '已支付', value: '10' }
      ]
    },
    colProps: { span: 8 }
  },
  {
    field: 'urgencyLevel',
    label: '紧急程度',
    component: 'Select',
    componentProps: {
      placeholder: '请选择紧急程度',
      options: [
        { label: '全部', value: '' },
        { label: '紧急', value: '紧急' },
        { label: '一般', value: '一般' },
        { label: '不紧急', value: '不紧急' }
      ]
    },
    colProps: { span: 8 }
  },
  {
    field: 'type',
    label: '报修类型',
    component: 'Input',
    componentProps: {
      placeholder: '请输入报修类型'
    },
    colProps: { span: 8 }
  }
];

// 注册搜索表单
const [registerForm, { getFieldsValue, resetFields }] = useForm({
  labelWidth: 80,
  schemas: searchFormSchema,
  autoSubmitOnEnter: true,
  submitButtonOptions: {
    text: '查询'
  },
  resetButtonOptions: {
    text: '重置'
  },
  actionColOptions: {
    span: 24
  },
  baseColProps: {
    span: 8
  }
});

// 主表列配置
const columns = [
  {
    title: '企微审批编号',
    dataIndex: 'spNo',
    width: 150,
    ellipsis: true
  },
  {
    title: '审批状态',
    dataIndex: 'spStatus',
    width: 100,
    slots: { customRender: 'spStatus' }
  },
  {
    title: '紧急程度',
    dataIndex: 'urgencyLevel',
    width: 100,
    slots: { customRender: 'urgencyLevel' }
  },
  {
    title: '所属部门',
    dataIndex: 'department',
    width: 150,
    ellipsis: true
  },
  {
    title: '报修类型',
    dataIndex: 'type',
    width: 120
  },
  {
    title: '维修单位',
    dataIndex: 'unit',
    width: 150,
    ellipsis: true
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 150,
    customRender: ({ text }: { text: string }) => formatDate(text)
  },
  {
    title: '操作',
    key: 'action',
    width: 120,
    fixed: 'right' as const,
    slots: { customRender: 'action' }
  }
];

// 详情表列配置
const detailColumns = [
  {
    title: '联系电话',
    dataIndex: 'phoneNumber',
    width: 120
  },
  {
    title: '地点',
    dataIndex: 'place',
    width: 150
  },
  {
    title: '故障描述',
    dataIndex: 'faultDescription',
    ellipsis: true
  },
  {
    title: '附件',
    dataIndex: 'fileUrl',
    width: 100,
    slots: { customRender: 'fileUrl' }
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 150,
    customRender: ({ text }: { text: string }) => formatDate(text)
  }
];

// API接口
const getMaintenanceList = (params: any) => {
  return defHttp.get({
    url: '/instrument/maintenance/list',
    params: {
      ...params,
      instrumentId: currentInstrumentId.value
    }
  });
};


// 导出维修记录
const exportMaintenanceList = (params: any) => {
  return defHttp.get({
    url: '/instrument/maintenance/export',
    params: {
      ...params,
      instrumentId: currentInstrumentId.value
    },
    responseType: 'blob'
  });
};

// 注册表格
const [registerTable, { reload, getDataSource }] = useTable({
  api: getMaintenanceList,
  columns,
  useSearchForm: false,
  showTableSetting: false,
  bordered: true,
  showIndexColumn: false,
  canResize: false,
  expandRowByClick: true,
  pagination: {
    current: paginationInfo.current,
    pageSize: paginationInfo.pageSize,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total: number, range: [number, number]) => {
      return `显示 ${range[0]}-${range[1]} 条，共 ${total} 条记录`;
    },
    onChange: (page: number, pageSize: number) => {
      paginationInfo.current = page;
      paginationInfo.pageSize = pageSize;
    },
    onShowSizeChange: (current: number, size: number) => {
      paginationInfo.current = 1;
      paginationInfo.pageSize = size;
    }
  },
  beforeFetch: (params: any) => {
    const searchParams = getFieldsValue();
    return {
      ...params,
      ...searchParams,
      instrumentId: currentInstrumentId.value
    };
  },
  afterFetch: (data: any) => {
    // 更新分页信息
    if (data && data.total !== undefined) {
      paginationInfo.total = data.total;
    }
    return data;
  }
});
reload()


// 获取审批状态颜色
const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    '1': 'processing',
    '2': 'success',
    '3': 'error',
    '4': 'default',
    '6': 'warning',
    '7': 'default',
    '10': 'cyan'
  };
  return colorMap[status] || 'default';
};

// 获取审批状态文本
const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    '1': '审批中',
    '2': '已通过',
    '3': '已驳回',
    '4': '已撤销',
    '6': '通过后撤销',
    '7': '已删除',
    '10': '已支付'
  };
  return textMap[status] || '未知';
};

// 获取紧急程度颜色
const getUrgencyColor = (level: string) => {
  const colorMap: Record<string, string> = {
    '紧急': 'red',
    '一般': 'orange',
    '不紧急': 'green'
  };
  return colorMap[level] || 'default';
};

// 格式化日期
const formatDate = (date: string) => {
  return date ? dayjs(date).format('YYYY-MM-DD HH:mm:ss') : '-';
};

// 搜索
const handleSearch = () => {
  paginationInfo.current = 1; // 重置到第一页
  reload();
};

// 重置
const handleReset = () => {
  resetFields();
  paginationInfo.current = 1; // 重置到第一页
  reload();
};

// 刷新
const handlePrint = () => {
  let arr = [{
    tableData: []
  }]
  new hiprint.PrintTemplate({ template: panel }).print(arr);
};

// 导出Excel
const handleExport = async () => {
  try {
    exportLoading.value = true;
    const searchParams = getFieldsValue();
    const params = {
      ...searchParams,
      instrumentId: currentInstrumentId.value
    };

    const response = await exportMaintenanceList(params);

    // 创建下载链接
    const blob = new Blob([response], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;

    // 生成文件名
    const fileName = `仪器维修记录_${currentInstrumentId.value}_${dayjs().format('YYYY-MM-DD_HH-mm-ss')}.xlsx`;
    link.download = fileName;

    // 触发下载
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

    message.success('导出成功');
  } catch (error) {
    console.error('导出失败:', error);
    message.error('导出失败，请稍后重试');
  } finally {
    exportLoading.value = false;
  }
};

// 查看详情
const handleView = (_record: any) => {
  message.info('查看详情功能');
};

// 预览文件
const handlePreviewFile = (fileUrl: string) => {
  window.open(fileUrl, '_blank');
};

// 表格操作
const getTableAction = (record: any) => {
  return [
    {
      label: '查看',
      onClick: handleView.bind(null, record)
    }
  ];
};

// 表单提交事件
async function handleSubmit() {
  closeModal();
}
</script>

<style scoped>
.repair-list-container {
  padding: 16px;
}

.search-form {
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px 16px;
  background: #fff;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.pagination-info {
  color: #666;
  font-size: 14px;
}

.repair-table {
  background: #fff;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.expanded-content {
  padding: 16px;
  background: #f9f9f9;
}

.detail-section {
  margin-top: 16px;
}

.detail-section h4 {
  margin-bottom: 12px;
  color: #1890ff;
  font-weight: 500;
}

.no-detail {
  text-align: center;
  padding: 20px;
  color: #999;
}

:deep(.ant-table-pagination) {
  margin: 16px 0;
  text-align: right;
}

:deep(.ant-pagination-total-text) {
  color: #666;
}
</style>
