<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="title" @ok="handleSubmit" width="80%">
    <a-form ref="formRef" :model="formModel" @submit="handleSubmit" :label-col="labelCol" :wrapper-col="wrapperCol"
      :rules="validatorRules">

      <!-- 基本信息 -->
      <a-divider orientation="left">基本信息</a-divider>

      <a-row :gutter="16">
        <a-col :span="10">
          <a-form-item label="仪器编码" name="instrumentNo">
            <a-input disabled v-model:value="formModel.instrumentNo" placeholder="请输入仪器编码" />
          </a-form-item>
        </a-col>
        <a-col :span="10">
          <a-form-item label="仪器名称" name="instrumentName">
            <a-input disabled v-model:value="formModel.instrumentName" placeholder="请输入仪器名称" />
          </a-form-item>
        </a-col>
      </a-row>

      <a-row :gutter="16">
        <a-col :span="10">
          <a-form-item label="紧急程度" name="urgencyLevel">
            <a-select v-model:value="formModel.urgencyLevel" placeholder="请选择紧急程度">
              <a-select-option value="urgent">紧急</a-select-option>
              <a-select-option value="normal">一般</a-select-option>
              <a-select-option value="low">不紧急</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>
        <a-col :span="10">
          <a-form-item label="维修单位" name="unit">
            <a-input v-model:value="formModel.unit" placeholder="请输入维修单位" />
          </a-form-item>
        </a-col>

        <a-col :span="10">
          <a-form-item label="地点类型" name="place">
            <a-select v-model:value="formModel.place" placeholder="请选择地点类型">
              <a-select-option value="warehouse">仓库</a-select-option>
              <a-select-option value="research">研发</a-select-option>
              <a-select-option value="quality">质量</a-select-option>
              <a-select-option value="other">其他</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>

        <a-col :span="10">
          <a-form-item label="报修原因" name="reason">
            <a-textarea v-model:value="formModel.reason" placeholder="请详细描述报修原因" :rows="3" :maxlength="300"
              show-count />
          </a-form-item>
        </a-col>
      </a-row>

      <!-- 故障明细 -->
      <a-divider orientation="left">
        <span>故障明细</span>
        <a-button type="primary" size="small" @click="addDetail" style="margin-left: 16px;">
          <PlusOutlined />
          添加明细
        </a-button>
      </a-divider>

      <div v-for="(detail, index) in formModel.detailDOS" :key="detail.key" class="detail-item">
        <a-card size="small" :title="`故障明细 ${index + 1}`" class="detail-card">
          <template #extra>
            <a-button type="text" danger size="small" @click="removeDetail(index)"
              :disabled="formModel.detailDOS.length === 1">
              <MinusCircleOutlined />
              删除
            </a-button>
          </template>
          <a-row :gutter="16">
            <a-col :span="10">
              <a-form-item :label="'故障描述'" :name="['detailDOS', index, 'faultDescription']"
                :rules="[{ required: true, message: '请输入故障描述' }]">
                <a-textarea v-model:value="detail.faultDescription" placeholder="请详细描述故障情况" :rows="3" :maxlength="500"
                  show-count />
              </a-form-item>
            </a-col>
            <a-col :span="10">
              <a-form-item :label="'故障照片'" :name="['detailDOS', index, 'faultPhoto']"
                :rules="[{ required: true, message: '请上传故障照片' }]">
                <JUpload v-model:value="detail.faultPhoto" :multiple="true" :showProgress="true" :isShowTip="true"
                  text="点击上传" :maxCount="5" accept="image/*" />
              </a-form-item>
            </a-col>
            <a-col :span="10">
              <a-form-item :label="'联系电话'" :name="['detailDOS', index, 'phoneNumber']">
                <a-input v-model:value="detail.phoneNumber" placeholder="请输入联系电话（选填）" :maxlength="20" />
              </a-form-item>
            </a-col>
            <a-col :span="10">
              <a-form-item :label="'地点'" :name="['detailDOS', index, 'place']">
                <a-input v-model:value="detail.place" placeholder="请输入具体地点（选填）" :maxlength="50" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-card>
      </div>
    </a-form>
  </BasicModal>
</template>

<script lang="ts" setup>
import { reactive, ref, computed, unref } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { saveCheck } from '../instrumentManagement.api';
import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import JUpload from '/@/components/Form/src/jeecg/components/JUpload/JUpload.vue';
import { defHttp } from '/@/utils/http/axios';

// 定义明细项接口
interface DetailItem {
  key: string;
  phoneNumber: string;
  place: string;
  faultDescription: string;
  faultPhoto: string;
}

// 定义表单模型接口
interface FormModel {
  instrumentNo: string;
  instrumentName: string;
  urgencyLevel: string;
  reason: string;
  unit: string;
  place: string;
  // 写死的字段，不在表单中显示
  department: string; // 默认"质量管理中心"
  type: string; // 默认"设备设施"
  detailDOS: DetailItem[];
}

// Emits声明
const emit = defineEmits(['register', 'success']);
const formRef = ref();
const isUpdate = ref(false);
const isFooter = ref(true);

const labelCol = reactive({
  xs: { span: 24 },
  sm: { span: 6 },
});
const wrapperCol = reactive({
  xs: { span: 24 },
  sm: { span: 18 },
});

// 表单数据模型
const formModel = reactive<FormModel>({
  instrumentNo: '',
  instrumentName: '',
  urgencyLevel: '',
  reason: '',
  unit: '',
  place: '',
  // 写死的字段
  department: '质量管理中心',
  type: '设备设施',
  detailDOS: [
    {
      key: Date.now().toString(),
      phoneNumber: '',
      place: '',
      faultDescription: '',
      faultPhoto: ''
    }
  ]
});

// 表单验证规则
const validatorRules = {
  instrumentNo: [{ required: true, message: '请输入仪器编码' }],
  instrumentName: [{ required: true, message: '请输入仪器名称' }],
  urgencyLevel: [{ required: true, message: '请选择紧急程度' }],
  reason: [{ required: true, message: '请输入报修原因' }],
  unit: [{ required: true, message: '请输入维修单位' }],
  place: [{ required: true, message: '请选择地点类型' }]
};

// 动态表单方法
// 添加明细（自动带入第一条的联系电话和地点）
function addDetail() {
  const firstDetail = formModel.detailDOS[0];
  formModel.detailDOS.push({
    key: Date.now().toString(),
    phoneNumber: firstDetail ? firstDetail.phoneNumber : '',
    place: firstDetail ? firstDetail.place : '',
    faultDescription: '',
    faultPhoto: ''
  });
}

// 删除明细
function removeDetail(index: number) {
  if (formModel.detailDOS.length > 1) {
    formModel.detailDOS.splice(index, 1);
  } else {
    message.warning('至少保留一条明细记录');
  }
}

// 重置表单
function resetForm() {
  formModel.instrumentNo = '';
  formModel.instrumentName = '';
  formModel.urgencyLevel = '';
  formModel.reason = '';
  formModel.unit = '';
  formModel.place = '';
  // 保持写死的字段值
  formModel.department = '质量管理中心';
  formModel.type = '设备设施';
  formModel.detailDOS = [
    {
      key: Date.now().toString(),
      phoneNumber: '',
      place: '',
      faultDescription: '',
      faultPhoto: ''
    }
  ];
}

//表单赋值
const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
  //重置表单
  if (formRef.value) {
    formRef.value.resetFields();
  }
  resetForm();

  setModalProps({ confirmLoading: false, showCancelBtn: data?.showFooter, showOkBtn: data?.showFooter });
  isUpdate.value = !!data?.isUpdate;
  isFooter.value = !!data?.showFooter;

  if (unref(isUpdate) && data.record) {
    // 赋值主表数据
    Object.assign(formModel, data.record);

    // 处理明细数据
    if (data.record.detailDOS && data.record.detailDOS.length > 0) {
      formModel.detailDOS = data.record.detailDOS.map((item: any) => ({
        ...item,
        key: item.id || Date.now().toString()
      }));
    }
  }

  console.log('🚀 ~ formModel:', formModel);
});

//设置标题
const title = computed(() => (!unref(isUpdate) ? '新增维修申请' : '编辑维修申请'));

//表单提交事件
function handleSubmit() {
  formRef.value
    .validate()
    .then(async () => {
      try {
        setModalProps({ confirmLoading: true });

        // 准备提交数据
        const submitData = {
          ...formModel,
          detailDOS: formModel.detailDOS.map(item => ({
            phoneNumber: item.phoneNumber,
            place: item.place,
            faultDescription: item.faultDescription,
            faultPhoto: item.faultPhoto
          }))
        };
        //提交表单
        await defHttp.post({ url: '/instrument/instrumentManagement/instrumentMaintenanceApproval', params: submitData });
        //关闭弹窗
        closeModal();
        //刷新列表
        emit('success', { isUpdate: isUpdate.value, data: submitData });
      } finally {
        setModalProps({ confirmLoading: false });
      }
    })
    .catch((error: any) => {
      console.log('表单验证失败:', error);
      message.error('请检查表单填写是否完整');
    });
}


</script>

<style lang="less" scoped>
.fontColor {
  color: black;
}

/* 明细项样式 */
.detail-item {
  margin-bottom: 16px;
}

.detail-card {
  border: 1px solid #d9d9d9;
  border-radius: 6px;

  :deep(.ant-card-head) {
    background-color: #fafafa;
    border-bottom: 1px solid #d9d9d9;

    .ant-card-head-title {
      font-weight: 500;
      color: #1890ff;
    }
  }

  :deep(.ant-card-body) {
    padding: 16px;
  }
}

/* 分割线样式 */
:deep(.ant-divider-horizontal.ant-divider-with-text-left) {
  margin: 24px 0 16px 0;

  .ant-divider-inner-text {
    font-weight: 500;
    color: #1890ff;
  }
}

/* 表单项样式优化 */
:deep(.ant-form-item) {
  margin-bottom: 16px;
}

:deep(.ant-form-item-label) {
  font-weight: 500;
}

/* 按钮样式 */
:deep(.ant-btn) {
  border-radius: 4px;
}

/* 选择框和输入框样式 */
:deep(.ant-select),
:deep(.ant-input),
:deep(.ant-input-number) {
  border-radius: 4px;
}

/* 文本域样式 */
:deep(.ant-input) {

  &:focus,
  &:hover {
    border-color: #1890ff;
  }
}

/* 上传组件样式 */
:deep(.ant-upload-drag) {
  border-radius: 4px;
  border: 1px dashed #d9d9d9;

  &:hover {
    border-color: #1890ff;
  }
}
</style>