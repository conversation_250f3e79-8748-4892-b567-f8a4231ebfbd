const panel = {
  panels: [
    {
      index: 0,
      name: 1,
      height: 297,
      width: 210,
      paperHeader: 49.5,
      paperFooter: 810,
      printElements: [
        {
          options: {
            left: 0,
            top: 0,
            height: 48,
            width: 594,
            title: '仪器设备故障维修登记表',
            right: 115.9921875,
            bottom: 6.4921875,
            vCenter: 55.9921875,
            hCenter: 1.6171875,
            coordinateSync: false,
            widthHeightSync: false,
            fontFamily: 'Microsoft YaHei',
            fontSize: 20,
            fontWeight: '700',
            letterSpacing: 3,
            textAlign: 'center',
            textContentVerticalAlign: 'middle',
            qrCodeLevel: 0,
          },
          printElementType: { title: '文本', type: 'text' },
        },
        {
          options: {
            left: 2.5,
            top: 52.5,
            height: 672,
            width: 588,
            right: 589.74609375,
            bottom: 90.24609375,
            vCenter: 295.74609375,
            hCenter: 72.24609375,
            columns: [
              [
                { width: 59.799688966882876, title: '等级时间', field: '', checked: true, columnId: '', fixed: false, rowspan: 1, colspan: 1 },
                { width: 85.11935353947584, title: '仪器名称', field: '', checked: true, columnId: '', fixed: false, rowspan: 1, colspan: 1 },
                { width: 82.1084271529241, title: '仪器编号', field: '', checked: true, columnId: '', fixed: false, rowspan: 1, colspan: 1 },
                { width: 93.08127785744095, title: '故障描述', field: '', checked: true, columnId: '', fixed: false, rowspan: 1, colspan: 1 },
                { width: 138.06340802406305, title: '维修单位', field: '', checked: true, columnId: '', fixed: false, rowspan: 1, colspan: 1 },
                { width: 60.074905492330316, title: '维修日期', field: '', checked: true, columnId: '', fixed: false, rowspan: 1, colspan: 1 },
                { width: 69.75293896688282, title: '备注', field: '', checked: true, columnId: '', fixed: false, rowspan: 1, colspan: 1 },
              ],
            ],
          },
          printElementType: {
            title: '空白表格',
            type: 'table',
            editable: true,
            columnDisplayEditable: true,
            columnDisplayIndexEditable: true,
            columnTitleEditable: true,
            columnResizable: true,
            columnAlignEditable: true,
            isEnableEditField: true,
            isEnableContextMenu: true,
            isEnableInsertRow: true,
            isEnableDeleteRow: true,
            isEnableInsertColumn: true,
            isEnableDeleteColumn: true,
            isEnableMergeCell: true,
          },
        },
      ],
      paperNumberLeft: 565.5,
      paperNumberTop: 819,
      paperNumberDisabled: true,
      paperNumberContinue: true,
      watermarkOptions: { content: 'vue-plugin-hiprint', rotate: 25, timestamp: true, format: 'YYYY-MM-DD HH:mm' },
      panelLayoutOptions: {},
    },
  ],
};

export default panel;
