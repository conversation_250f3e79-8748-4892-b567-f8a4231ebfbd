<template>
  <div class="maintenance-table-container">
    <!-- 搜索区域 -->
    <div class="search-form">
      <a-form layout="inline" :model="searchForm">
        <a-form-item label="仪器ID">
          <a-input v-model:value="searchForm.instrumentId" placeholder="请输入仪器ID" />
        </a-form-item>
        <a-form-item label="审批状态">
          <a-select v-model:value="searchForm.spStatus" placeholder="请选择审批状态" style="width: 150px">
            <a-select-option value="">全部</a-select-option>
            <a-select-option value="1">审批中</a-select-option>
            <a-select-option value="2">已通过</a-select-option>
            <a-select-option value="3">已驳回</a-select-option>
            <a-select-option value="4">已撤销</a-select-option>
            <a-select-option value="6">通过后撤销</a-select-option>
            <a-select-option value="7">已删除</a-select-option>
            <a-select-option value="10">已支付</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="报修类型">
          <a-input v-model:value="searchForm.type" placeholder="请输入报修类型" />
        </a-form-item>
        <a-form-item>
          <a-button type="primary" @click="handleSearch">
            <template #icon><SearchOutlined /></template>
            搜索
          </a-button>
          <a-button style="margin-left: 8px" @click="handleReset">
            <template #icon><ReloadOutlined /></template>
            重置
          </a-button>
        </a-form-item>
      </a-form>
    </div>

    <!-- 表格区域 -->
    <a-table
      :columns="columns"
      :data-source="tableData"
      :loading="loading"
      :pagination="pagination"
      :expandedRowKeys="expandedRowKeys"
      @expand="onExpand"
      @change="handleTableChange"
      row-key="id"
      size="middle"
    >
      <!-- 审批状态列 -->
      <template #spStatus="{ record }">
        <a-tag :color="getStatusColor(record.spStatus)">
          {{ getStatusText(record.spStatus) }}
        </a-tag>
      </template>

      <!-- 紧急程度列 -->
      <template #urgencyLevel="{ record }">
        <a-tag :color="getUrgencyColor(record.urgencyLevel)">
          {{ record.urgencyLevel }}
        </a-tag>
      </template>

      <!-- 操作列 -->
      <template #action="{ record }">
        <a-space>
          <a-button type="link" size="small" @click="handleView(record)">
            <template #icon><EyeOutlined /></template>
            查看
          </a-button>
          <a-button type="link" size="small" @click="handleEdit(record)">
            <template #icon><EditOutlined /></template>
            编辑
          </a-button>
          <a-popconfirm
            title="确定要删除这条记录吗？"
            @confirm="handleDelete(record)"
          >
            <a-button type="link" size="small" danger>
              <template #icon><DeleteOutlined /></template>
              删除
            </a-button>
          </a-popconfirm>
        </a-space>
      </template>

      <!-- 展开行内容 -->
      <template #expandedRowRender="{ record }">
        <div class="expanded-content">
          <a-descriptions title="维修详细信息" :column="2" bordered size="small">
            <a-descriptions-item label="企微审批编号">
              {{ record.spNo || '-' }}
            </a-descriptions-item>
            <a-descriptions-item label="维修单位">
              {{ record.unit || '-' }}
            </a-descriptions-item>
            <a-descriptions-item label="地点类型">
              {{ record.place || '-' }}
            </a-descriptions-item>
            <a-descriptions-item label="创建时间">
              {{ formatDate(record.createTime) }}
            </a-descriptions-item>
            <a-descriptions-item label="创建人">
              {{ record.creator || '-' }}
            </a-descriptions-item>
            <a-descriptions-item label="报修原因" :span="2">
              {{ record.reason || '-' }}
            </a-descriptions-item>
          </a-descriptions>

          <!-- 维修详情列表 -->
          <div class="detail-section" v-if="record.details && record.details.length > 0">
            <h4>维修详情记录</h4>
            <a-table
              :columns="detailColumns"
              :data-source="record.details"
              :pagination="false"
              size="small"
              row-key="id"
            >
              <template #fileUrl="{ record: detail }">
                <a-space v-if="detail.fileUrl">
                  <a-button
                    type="link"
                    size="small"
                    @click="handlePreviewFile(detail.fileUrl)"
                  >
                    <template #icon><FileTextOutlined /></template>
                    查看附件
                  </a-button>
                </a-space>
                <span v-else>-</span>
              </template>
            </a-table>
          </div>
          <div v-else class="no-detail">
            <a-empty description="暂无维修详情记录" />
          </div>
        </div>
      </template>
    </a-table>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { message } from 'ant-design-vue'
import {
  SearchOutlined,
  ReloadOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  FileTextOutlined
} from '@ant-design/icons-vue'
import dayjs from 'dayjs'
import {
  getMaintenanceList,
  getMaintenanceDetailList,
  deleteMaintenance,
  getFilePreviewUrl
} from '/@/api/instrumentManagement/maintenance'

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const expandedRowKeys = ref([])

// 搜索表单
const searchForm = reactive({
  instrumentId: '',
  spStatus: '',
  type: ''
})

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total) => `共 ${total} 条记录`
})

// 主表列配置
const columns = [
  {
    title: '仪器ID',
    dataIndex: 'instrumentId',
    key: 'instrumentId',
    width: 120,
    ellipsis: true
  },
  {
    title: '审批状态',
    dataIndex: 'spStatus',
    key: 'spStatus',
    width: 100,
    slots: { customRender: 'spStatus' }
  },
  {
    title: '紧急程度',
    dataIndex: 'urgencyLevel',
    key: 'urgencyLevel',
    width: 100,
    slots: { customRender: 'urgencyLevel' }
  },
  {
    title: '所属部门',
    dataIndex: 'department',
    key: 'department',
    width: 150,
    ellipsis: true
  },
  {
    title: '报修类型',
    dataIndex: 'type',
    key: 'type',
    width: 120
  },
  {
    title: '维修单位',
    dataIndex: 'unit',
    key: 'unit',
    width: 150,
    ellipsis: true
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 150,
    customRender: ({ text }) => formatDate(text)
  },
  {
    title: '操作',
    key: 'action',
    width: 180,
    fixed: 'right',
    slots: { customRender: 'action' }
  }
]

// 详情表列配置
const detailColumns = [
  {
    title: '联系电话',
    dataIndex: 'phoneNumber',
    key: 'phoneNumber',
    width: 120
  },
  {
    title: '地点',
    dataIndex: 'place',
    key: 'place',
    width: 150
  },
  {
    title: '故障描述',
    dataIndex: 'faultDescription',
    key: 'faultDescription',
    ellipsis: true
  },
  {
    title: '附件',
    dataIndex: 'fileUrl',
    key: 'fileUrl',
    width: 100,
    slots: { customRender: 'fileUrl' }
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 150,
    customRender: ({ text }) => formatDate(text)
  }
]

// 获取审批状态颜色
const getStatusColor = (status) => {
  const colorMap = {
    '1': 'processing',
    '2': 'success',
    '3': 'error',
    '4': 'default',
    '6': 'warning',
    '7': 'default',
    '10': 'cyan'
  }
  return colorMap[status] || 'default'
}

// 获取审批状态文本
const getStatusText = (status) => {
  const textMap = {
    '1': '审批中',
    '2': '已通过',
    '3': '已驳回',
    '4': '已撤销',
    '6': '通过后撤销',
    '7': '已删除',
    '10': '已支付'
  }
  return textMap[status] || '未知'
}

// 获取紧急程度颜色
const getUrgencyColor = (level) => {
  const colorMap = {
    '紧急': 'red',
    '一般': 'orange',
    '不紧急': 'green'
  }
  return colorMap[level] || 'default'
}

// 格式化日期
const formatDate = (date) => {
  return date ? dayjs(date).format('YYYY-MM-DD HH:mm:ss') : '-'
}

// 展开行事件
const onExpand = async (expanded, record) => {
  if (expanded && (!record.details || record.details.length === 0)) {
    // 加载详情数据
    await loadDetailData(record.id)
  }
}

// 加载详情数据
const loadDetailData = async (mainId) => {
  try {
    const response = await getMaintenanceDetailList(mainId)

    // 更新对应记录的详情数据
    const index = tableData.value.findIndex(item => item.id === mainId)
    if (index !== -1) {
      tableData.value[index].details = response.result || []
    }
  } catch (error) {
    console.error('加载详情数据失败:', error)
    message.error('加载详情数据失败')
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  loadData()
}

// 重置
const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  pagination.current = 1
  loadData()
}

// 表格变化事件
const handleTableChange = (pag) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadData()
}

// 查看
const handleView = (record) => {
  console.log('查看记录:', record)
  // 实现查看逻辑
}

// 编辑
const handleEdit = (record) => {
  console.log('编辑记录:', record)
  // 实现编辑逻辑
}

// 删除
const handleDelete = async (record) => {
  try {
    await deleteMaintenance(record.id)
    message.success('删除成功')
    loadData()
  } catch (error) {
    console.error('删除失败:', error)
    message.error('删除失败')
  }
}

// 预览文件
const handlePreviewFile = (fileUrl) => {
  const previewUrl = getFilePreviewUrl(fileUrl)
  window.open(previewUrl, '_blank')
}

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    const params = {
      ...searchForm,
      pageNo: pagination.current,
      pageSize: pagination.pageSize
    }

    const response = await getMaintenanceList(params)

    if (response.success) {
      tableData.value = response.result.records || []
      pagination.total = response.result.total || 0
    } else {
      message.error(response.message || '加载数据失败')
    }
  } catch (error) {
    console.error('加载数据失败:', error)
    message.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.maintenance-table-container {
  padding: 16px;
  background: #fff;
}

.search-form {
  margin-bottom: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.expanded-content {
  padding: 16px;
  background: #f9f9f9;
}

.detail-section {
  margin-top: 16px;
}

.detail-section h4 {
  margin-bottom: 12px;
  color: #1890ff;
}

.no-detail {
  text-align: center;
  padding: 20px;
}
</style>
