<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="'仪器维修'" @ok="handleSubmit" :width="1400">
  </BasicModal>
</template>
<script lang="ts" setup>
  import { ref, computed, unref, inject } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, useForm, FormSchema } from '/@/components/Form/index';
  import { saveForm1 } from '../instrumentManagement.api';
  import { defHttp1 } from '/@/utils/http/axios/index1';
  import { useUserStore } from '/@/store/modules/user';
  const userStore = useUserStore();

  // 声明Emits
  const emit = defineEmits(['success', 'register']);

 
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
   
    setModalProps({ confirmLoading: false });
    
  });

  //表单提交事件
  async function handleSubmit() {
          closeModal();
  }

</script>
