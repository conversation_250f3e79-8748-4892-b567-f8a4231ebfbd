<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="'仪器维修'" @ok="handleSubmit" :width="1400">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
<script lang="ts" setup>
  import { ref, computed, unref, inject } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { BasicForm, useForm, FormSchema } from '/@/components/Form/index';
  import { saveForm1 } from '../instrumentManagement.api';
  import { defHttp1 } from '/@/utils/http/axios/index1';
  import { useUserStore } from '/@/store/modules/user';
  const userStore = useUserStore();

  const customerFormSchema: FormSchema[] = [
    {
      label: '',
      field: 'parentId',
      component: 'Input',
      show: false,
    },
    {
      label: '',
      field: 'instrumentNo',
      component: 'Input',
      show: false,
    },
    {
      label: '',
      field: 'instrumentName',
      component: 'Input',
      show: false,
    },
    {
      label: '',
      field: 'instrumentModel',
      component: 'Input',
      show: false,
    },
    {
      label: '',
      field: 'instrumentBrand',
      component: 'Input',
      show: false,
    },
    {
      label: '',
      field: 'storagePlace',
      component: 'Input',
      show: false,
    },
    {
      label: '报修日期',
      field: 'repairDate',
      component: 'DatePicker',
      required: true,
      defaultValue: new Date(),
      componentProps: {
        valueFormat: 'YYYY-MM-DD',
      },
      colProps: { span: 24 },
      rules: [
        {
          required: true,
          // @ts-ignore
          validator: async (rule, value) => {
            if (!value) {
              /* eslint-disable-next-line */
              return Promise.reject('值不能为空');
            }
            if (isFuture(value)) {
              /* eslint-disable-next-line */
              return Promise.reject('不能选择未来的日期');
            }
            return Promise.resolve();
          },
          trigger: 'change',
        },
      ],
    },
    {
      label: '报修人',
      field: 'repairApplicant',
      component: 'Input',
      required: true,
      defaultValue: userStore.getUserInfo.realname,
      colProps: { span: 24 },
    },
    {
      label: '维修单位',
      field: 'repairUnit',
      component: 'Input',
      required: false,
      colProps: { span: 24 },
    },
    {
      label: '故障描述及处理方法',
      field: 'faultDescription',
      component: 'InputTextArea',
      required: false,
      colProps: { span: 24 },
    },
    {
      label: '维修描述',
      field: 'repairDescription',
      component: 'InputTextArea',
      required: false,
      colProps: { span: 24 },
    },
  ];
  //接收主表id
  const orderId = inject('orderId') || '';
  // 声明Emits
  const emit = defineEmits(['success', 'register']);
  const isUpdate = ref(true);
  const isSee = ref(true);
  //表单配置
  const [registerForm, { resetFields, setFieldsValue, validate, updateSchema }] = useForm({
    labelWidth: 150,
    schemas: customerFormSchema,
    showActionButtonGroup: false,
  });
  //表单赋值
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
   
    setModalProps({ confirmLoading: false });
    // isUpdate.value = !!data?.isUpdate;
    isSee.value = !!data?.isSee;
    if (unref(isSee)) {
      //表单赋值
      let params = {
        id: data.record.id,
      };
      await defHttp1.get({ url: '/instrument/instrumentManagementLog/queryById', params }).then((res) => {
        if (res.code == 200) {
          console.log();
          setFieldsValue({
            parentId: data.record.id,
            ...res.result[0],
          });
        }
      });
      await updateSchema([
        { field: 'repairDate', componentProps: { disabled: true } },
        { field: 'repairApplicant', componentProps: { disabled: true } },
        { field: 'repairUnit', componentProps: { disabled: true } },
        { field: 'faultDescription', componentProps: { disabled: true } },
        { field: 'repairDescription', componentProps: { disabled: true } },
      ]);
    } else {
      await setFieldsValue({
        parentId: data.record.id,
        ...data.record,
      });
    }
  });

  //表单提交事件
  async function handleSubmit() {
          closeModal();
  }

</script>
